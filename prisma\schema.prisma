datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum UserRole {
  USER
  ADMIN
}

model User {
  id            String          @id @default(cuid())
  name          String?
  email         String          @unique
  emailVerified DateTime?
  image         String?
  password      String?         // For credentials authentication
  role          UserRole        @default(USER) // User role for access control
  isActive      Boolean         @default(true) // Enable/disable user
  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]
  verificationCodes VerificationCode[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationCode {
  id        String   @id @default(cuid())
  code      String
  email     String
  userId    String?
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  type      String   @default("email_verification") // email_verification, password_reset, etc.
  expiresAt DateTime
  used      <PERSON>olean  @default(false)
  createdAt DateTime @default(now())

  @@index([email])
  @@index([code])
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

// Product Management Models
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  slug        String    @unique

  // Visual elements
  icon        String?   // Icon name or class
  image       String?   // Category image URL
  color       String?   // Hex color code for theming

  // SEO & Display
  metaTitle       String?
  metaDescription String?
  displayOrder    Int?      // For sorting categories

  isActive    Boolean   @default(true)
  products    Product[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([slug])
  @@index([isActive])
  @@index([displayOrder])
}

model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  sku         String   @unique
  slug        String   @unique

  // Pricing
  price       Decimal
  costPrice   Decimal?
  comparePrice Decimal?

  // Inventory
  stockQuantity Int     @default(0)
  lowStockThreshold Int @default(10)
  trackQuantity Boolean @default(true)

  // Physical properties
  weight      Decimal?
  dimensions  String?  // JSON string: {"length": 10, "width": 5, "height": 3}

  // Media
  images      String[] // Array of image URLs
  thumbnail   String?  // Main product image

  // SEO & Organization
  tags        String[] // Array of tags
  metaTitle   String?
  metaDescription String?

  // Status
  status      ProductStatus @default(DRAFT)
  isActive    Boolean       @default(true)
  isFeatured  Boolean       @default(false)

  // Relationships
  categoryId  String?
  category    Category? @relation(fields: [categoryId], references: [id])

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?

  @@index([sku])
  @@index([slug])
  @@index([status])
  @@index([isActive])
  @@index([categoryId])
  @@index([createdAt])
}

enum ProductStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Contact Messages Model
model ContactMessage {
  id          String   @id @default(cuid())
  name        String
  email       String
  phone       String?
  company     String?
  subject     String
  message     String

  // Status tracking
  status      MessageStatus @default(UNREAD)
  priority    MessagePriority @default(NORMAL)

  // Admin response
  adminResponse String?
  respondedAt   DateTime?
  respondedBy   String?

  // Metadata
  ipAddress     String?
  userAgent     String?
  source        String? // "landing_page", "contact_page", etc.

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([email])
}

enum MessageStatus {
  UNREAD
  READ
  IN_PROGRESS
  RESOLVED
  ARCHIVED
}

enum MessagePriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
