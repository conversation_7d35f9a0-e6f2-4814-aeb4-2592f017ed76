"use client";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
    ChevronRight, 
    Stethoscope, 
    Scissors, 
    Zap, 
    Shield, 
    Wrench, 
    Syringe,
    Microscope,
    Heart
} from "lucide-react";
import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";

interface Category {
    id: string;
    name: string;
    slug: string;
    description: string;
    icon: React.ReactNode;
    image: string;
    productCount: number;
    color: string;
}

function CategoriesSection() {
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);

    // Default categories with French-inspired names and styling
    const defaultCategories: Category[] = [
        {
            id: "composite-adhesif",
            name: "Composite & Adhésif",
            slug: "composite-adhesif",
            description: "Matériaux de restauration de haute qualité pour des résultats durables",
            icon: <Heart className="h-6 w-6" />,
            image: dentalequipment.src,
            productCount: 24,
            color: "from-blue-500 to-blue-600"
        },
        {
            id: "instruments",
            name: "Instruments Dentaires",
            slug: "instruments",
            description: "Instruments de précision pour tous vos besoins cliniques",
            icon: <Scissors className="h-6 w-6" />,
            image: dentalequipment.src,
            productCount: 45,
            color: "from-green-500 to-green-600"
        },
        {
            id: "equipement",
            name: "Équipement Médical",
            slug: "equipement",
            description: "Technologies avancées pour votre cabinet dentaire",
            icon: <Zap className="h-6 w-6" />,
            image: dentalequipment.src,
            productCount: 18,
            color: "from-purple-500 to-purple-600"
        },
        {
            id: "sterilisation",
            name: "Stérilisation",
            slug: "sterilisation",
            description: "Solutions complètes pour l'hygiène et la sécurité",
            icon: <Shield className="h-6 w-6" />,
            image: dentalequipment.src,
            productCount: 32,
            color: "from-orange-500 to-orange-600"
        },
        {
            id: "diagnostic",
            name: "Diagnostic",
            slug: "diagnostic",
            description: "Outils de diagnostic de pointe pour un traitement précis",
            icon: <Microscope className="h-6 w-6" />,
            image: dentalequipment.src,
            productCount: 28,
            color: "from-teal-500 to-teal-600"
        },
        {
            id: "maintenance",
            name: "Maintenance",
            slug: "maintenance",
            description: "Services et pièces pour l'entretien de vos équipements",
            icon: <Wrench className="h-6 w-6" />,
            image: dentalequipment.src,
            productCount: 15,
            color: "from-red-500 to-red-600"
        }
    ];

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                const response = await fetch('/api/categories?isActive=true');
                if (response.ok) {
                    const data = await response.json();
                    const apiCategories = data.categories || [];
                    
                    if (apiCategories.length > 0) {
                        // Map API categories to our format
                        const mappedCategories = apiCategories.slice(0, 6).map((cat: any, index: number) => ({
                            id: cat.id,
                            name: cat.name,
                            slug: cat.slug,
                            description: cat.description || defaultCategories[index]?.description || "Découvrez notre sélection de produits",
                            icon: defaultCategories[index]?.icon || <Stethoscope className="h-6 w-6" />,
                            image: dentalequipment.src,
                            productCount: Math.floor(Math.random() * 50) + 10, // Random count for demo
                            color: defaultCategories[index]?.color || "from-blue-500 to-blue-600"
                        }));
                        setCategories(mappedCategories);
                    } else {
                        setCategories(defaultCategories);
                    }
                } else {
                    setCategories(defaultCategories);
                }
            } catch (error) {
                console.error('Failed to fetch categories:', error);
                setCategories(defaultCategories);
            } finally {
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    return (
        <section className="py-24 md:py-32 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
                <div className="absolute bottom-20 right-10 w-72 h-72 bg-green-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
                {/* Section Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true, margin: "-100px" }}
                    className="text-center mb-20"
                >
                    <Badge className="mb-6 bg-gradient-to-r from-blue-100 to-green-100 text-blue-700 hover:from-blue-200 hover:to-green-200 shadow-sm border-0 px-6 py-2">
                        <Stethoscope className="h-4 w-4 mr-2" />
                        Découvrez
                    </Badge>

                    <h2 className="text-5xl md:text-6xl font-bold mb-6">
                        <span className="text-gray-900">Nos </span>
                        <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-600">
                            Catégories
                        </span>
                    </h2>

                    <div className="w-32 h-1.5 bg-gradient-to-r from-green-500 to-blue-600 mx-auto mb-8 rounded-full"></div>

                    <p className="text-xl text-gray-600 max-w-4xl mx-auto font-medium leading-relaxed">
                        Profitez de nos offres exceptionnelles sur une sélection d&apos;équipements médicaux 
                        de qualité. Nous proposons des réductions intéressantes sur des produits 
                        essentiels pour les professionnels de santé.
                    </p>
                </motion.div>

                {/* Categories Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {categories.map((category, index) => (
                        <motion.div
                            key={category.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true, margin: "-50px" }}
                            className="group"
                        >
                            <Card className="relative h-80 overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 rounded-2xl group-hover:shadow-blue-200/50">
                                {/* Background Image */}
                                <div className="absolute inset-0">
                                    <Image
                                        src={category.image}
                                        alt={category.name}
                                        fill
                                        className="object-cover transition-transform duration-700 group-hover:scale-110"
                                    />
                                    {/* Gradient Overlay */}
                                    <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-80 group-hover:opacity-90 transition-opacity duration-300`}></div>
                                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                                </div>

                                {/* Content */}
                                <div className="relative z-10 h-full flex flex-col justify-between p-8 text-white">
                                    <div>
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl group-hover:bg-white/30 transition-colors duration-300">
                                                {category.icon}
                                            </div>
                                            <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30">
                                                {category.productCount} produits
                                            </Badge>
                                        </div>

                                        <div className="mb-6">
                                            <p className="text-sm font-medium text-white/90 mb-2 uppercase tracking-wider">
                                                NOS CATÉGORIES
                                            </p>
                                            <h3 className="text-2xl font-bold mb-3 group-hover:text-white transition-colors">
                                                {category.name}
                                            </h3>
                                            <p className="text-white/90 text-sm leading-relaxed">
                                                {category.description}
                                            </p>
                                        </div>
                                    </div>

                                    <div>
                                        <Button
                                            className="w-full bg-white/20 backdrop-blur-sm hover:bg-white hover:text-gray-900 border-white/30 text-white font-semibold py-3 rounded-xl transition-all duration-300 group-hover:shadow-lg"
                                            asChild
                                        >
                                            <Link href={`/catalog?category=${category.slug}`}>
                                                Découvrez
                                                <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </Card>
                        </motion.div>
                    ))}
                </div>

                {/* Call to Action */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true, margin: "-100px" }}
                    className="mt-20 text-center"
                >
                    <div className="bg-white rounded-3xl p-8 md:p-12 max-w-4xl mx-auto shadow-xl border border-gray-100">
                        <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Explorez Toutes Nos Catégories
                        </h3>
                        <p className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg">
                            Découvrez notre gamme complète d&apos;équipements dentaires et médicaux. 
                            Des solutions innovantes pour tous vos besoins professionnels.
                        </p>
                        <Button
                            size="lg"
                            className="rounded-full gap-2 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all px-8 py-4 text-lg"
                            asChild
                        >
                            <Link href="/catalog">
                                Voir Tout Le Catalogue
                                <ChevronRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}

export default CategoriesSection;
