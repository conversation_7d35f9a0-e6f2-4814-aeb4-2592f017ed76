"use client";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
    ChevronRight,
    ChevronLeft,
    Stethoscope,
    Scissors,
    Zap,
    Shield,
    Wrench,
    Syringe,
    Microscope,
    Heart,
    Play,
    Pause
} from "lucide-react";
import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";

interface Category {
    id: string;
    name: string;
    slug: string;
    description: string;
    icon: React.ReactNode;
    image: string;
    productCount: number;
}

function CategoriesSection() {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(true);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    // 5 categories with French names and blue theme
    const categories: Category[] = [
        {
            id: "composite-adhesif",
            name: "Composite & Adhésif",
            slug: "composite-adhesif",
            description: "Matériaux de restauration de haute qualité pour des résultats durables et esthétiques",
            icon: <Heart className="h-8 w-8" />,
            image: dentalequipment.src,
            productCount: 24
        },
        {
            id: "instruments",
            name: "Instruments Dentaires",
            slug: "instruments",
            description: "Instruments de précision pour tous vos besoins cliniques quotidiens",
            icon: <Scissors className="h-8 w-8" />,
            image: dentalequipment.src,
            productCount: 45
        },
        {
            id: "equipement",
            name: "Équipement Médical",
            slug: "equipement",
            description: "Technologies avancées pour moderniser votre cabinet dentaire",
            icon: <Zap className="h-8 w-8" />,
            image: dentalequipment.src,
            productCount: 18
        },
        {
            id: "sterilisation",
            name: "Stérilisation",
            slug: "sterilisation",
            description: "Solutions complètes pour l'hygiène et la sécurité de vos patients",
            icon: <Shield className="h-8 w-8" />,
            image: dentalequipment.src,
            productCount: 32
        },
        {
            id: "diagnostic",
            name: "Diagnostic",
            slug: "diagnostic",
            description: "Outils de diagnostic de pointe pour un traitement précis et efficace",
            icon: <Microscope className="h-8 w-8" />,
            image: dentalequipment.src,
            productCount: 28
        }
    ];

    // Auto-play functionality
    useEffect(() => {
        if (isAutoPlaying) {
            intervalRef.current = setInterval(() => {
                setCurrentIndex((prevIndex) =>
                    prevIndex === categories.length - 1 ? 0 : prevIndex + 1
                );
            }, 4000);
        } else {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        }

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isAutoPlaying, categories.length]);

    const nextSlide = () => {
        setCurrentIndex((prevIndex) =>
            prevIndex === categories.length - 1 ? 0 : prevIndex + 1
        );
    };

    const prevSlide = () => {
        setCurrentIndex((prevIndex) =>
            prevIndex === 0 ? categories.length - 1 : prevIndex - 1
        );
    };

    const goToSlide = (index: number) => {
        setCurrentIndex(index);
    };

    const toggleAutoPlay = () => {
        setIsAutoPlaying(!isAutoPlaying);
    };

    return (
        <section className="py-24 md:py-32 px-4 sm:px-6 lg:px-8 bg-white relative overflow-hidden">
            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-96 h-96 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
                <div className="absolute bottom-20 right-10 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-50 to-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
                {/* Section Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true, margin: "-100px" }}
                    className="text-center mb-20"
                >
                    <Badge className="mb-6 bg-blue-100 text-blue-700 hover:bg-blue-200 shadow-sm border-0 px-6 py-2">
                        <Stethoscope className="h-4 w-4 mr-2" />
                        Découvrez
                    </Badge>

                    <h2 className="text-5xl md:text-6xl font-bold mb-6">
                        <span className="text-gray-900">Nos </span>
                        <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-blue-700">
                            Catégories
                        </span>
                    </h2>

                    <div className="w-32 h-1.5 bg-gradient-to-r from-blue-500 to-blue-700 mx-auto mb-8 rounded-full"></div>

                    <p className="text-xl text-gray-600 max-w-4xl mx-auto font-medium leading-relaxed">
                        Profitez de nos offres exceptionnelles sur une sélection d&apos;équipements médicaux
                        de qualité. Nous proposons des réductions intéressantes sur des produits
                        essentiels pour les professionnels de santé.
                    </p>
                </motion.div>

                {/* Innovative Carousel */}
                <div className="relative max-w-6xl mx-auto">
                    {/* Main Carousel Container */}
                    <div className="relative h-[500px] overflow-hidden rounded-3xl bg-gradient-to-br from-blue-50 to-blue-100 shadow-2xl">
                        {/* Carousel Content */}
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={currentIndex}
                                initial={{ opacity: 0, x: 300, scale: 0.8 }}
                                animate={{ opacity: 1, x: 0, scale: 1 }}
                                exit={{ opacity: 0, x: -300, scale: 0.8 }}
                                transition={{
                                    duration: 0.6,
                                    ease: [0.25, 0.46, 0.45, 0.94]
                                }}
                                className="absolute inset-0 flex items-center justify-center p-12"
                            >
                                <div className="text-center max-w-4xl">
                                    {/* Icon with Animation */}
                                    <motion.div
                                        initial={{ scale: 0, rotate: -180 }}
                                        animate={{ scale: 1, rotate: 0 }}
                                        transition={{ delay: 0.2, duration: 0.5 }}
                                        className="mb-8"
                                    >
                                        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center shadow-xl">
                                            <div className="text-white">
                                                {categories[currentIndex]?.icon}
                                            </div>
                                        </div>
                                    </motion.div>

                                    {/* Category Name */}
                                    <motion.h3
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.3, duration: 0.5 }}
                                        className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
                                    >
                                        {categories[currentIndex]?.name}
                                    </motion.h3>

                                    {/* Description */}
                                    <motion.p
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.4, duration: 0.5 }}
                                        className="text-xl text-gray-600 mb-8 leading-relaxed max-w-3xl mx-auto"
                                    >
                                        {categories[currentIndex]?.description}
                                    </motion.p>

                                    {/* Product Count Badge */}
                                    <motion.div
                                        initial={{ opacity: 0, scale: 0 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: 0.5, duration: 0.3 }}
                                        className="mb-8"
                                    >
                                        <Badge className="bg-blue-600 text-white px-6 py-2 text-lg font-semibold">
                                            {categories[currentIndex]?.productCount} produits disponibles
                                        </Badge>
                                    </motion.div>

                                    {/* CTA Button */}
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.6, duration: 0.5 }}
                                    >
                                        <Button
                                            size="lg"
                                            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                                            asChild
                                        >
                                            <Link href={`/catalog?category=${categories[currentIndex]?.slug}`}>
                                                Découvrez
                                                <ChevronRight className="ml-2 h-5 w-5" />
                                            </Link>
                                        </Button>
                                    </motion.div>
                                </div>
                            </motion.div>
                        </AnimatePresence>

                        {/* Navigation Arrows */}
                        <button
                            onClick={prevSlide}
                            className="absolute left-6 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 z-10"
                        >
                            <ChevronLeft className="h-6 w-6 text-blue-600" />
                        </button>

                        <button
                            onClick={nextSlide}
                            className="absolute right-6 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 z-10"
                        >
                            <ChevronRight className="h-6 w-6 text-blue-600" />
                        </button>

                        {/* Auto-play Control */}
                        <button
                            onClick={toggleAutoPlay}
                            className="absolute top-6 right-6 w-10 h-10 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 z-10"
                        >
                            {isAutoPlaying ? (
                                <Pause className="h-4 w-4 text-blue-600" />
                            ) : (
                                <Play className="h-4 w-4 text-blue-600" />
                            )}
                        </button>
                    </div>

                    {/* Dots Indicator */}
                    <div className="flex justify-center mt-8 space-x-3">
                        {categories.map((_, index) => (
                            <button
                                key={index}
                                onClick={() => goToSlide(index)}
                                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                    index === currentIndex
                                        ? 'bg-blue-600 w-8'
                                        : 'bg-blue-200 hover:bg-blue-300'
                                }`}
                            />
                        ))}
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-6 max-w-md mx-auto">
                        <div className="w-full bg-blue-100 rounded-full h-1">
                            <motion.div
                                className="bg-blue-600 h-1 rounded-full"
                                initial={{ width: "0%" }}
                                animate={{ width: `${((currentIndex + 1) / categories.length) * 100}%` }}
                                transition={{ duration: 0.3 }}
                            />
                        </div>
                        <p className="text-center text-sm text-gray-500 mt-2">
                            {currentIndex + 1} sur {categories.length}
                        </p>
                    </div>
                </div>

                {/* Call to Action */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true, margin: "-100px" }}
                    className="mt-20 text-center"
                >
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8 md:p-12 max-w-4xl mx-auto shadow-xl border border-blue-200">
                        <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Explorez Toutes Nos Catégories
                        </h3>
                        <p className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg">
                            Découvrez notre gamme complète d&apos;équipements dentaires et médicaux.
                            Des solutions innovantes pour tous vos besoins professionnels.
                        </p>
                        <Button
                            size="lg"
                            className="rounded-full gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all px-8 py-4 text-lg"
                            asChild
                        >
                            <Link href="/catalog">
                                Voir Tout Le Catalogue
                                <ChevronRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}

export default CategoriesSection;
