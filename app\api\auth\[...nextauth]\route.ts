import { handlers } from "@/auth" // Referring to the auth.ts we just created
import { NextRequest, NextResponse } from "next/server"

// Wrap handlers with error handling
const wrappedGET = async (req: NextRequest) => {
  try {
    return await handlers.GET(req)
  } catch (error) {
    console.error("Auth GET error:", error)
    return NextResponse.json(
      { error: "Authentication error" },
      { status: 500 }
    )
  }
}

const wrappedPOST = async (req: NextRequest) => {
  try {
    return await handlers.POST(req)
  } catch (error) {
    console.error("Auth POST error:", error)
    return NextResponse.json(
      { error: "Authentication error" },
      { status: 500 }
    )
  }
}

export { wrappedGET as GET, wrappedPOST as POST }