"use client";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ton } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
    ArrowRight,
    Heart,
    Scissors,
    Zap,
    Shield,
    Microscope,
    Syringe
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";

function ProductGridSection() {
    const categories = [
        {
            id: 1,
            name: "Composite & Adhésif",
            description: "Matériaux de restauration dentaire",
            icon: <Heart className="h-8 w-8" />,
            image: dentalequipment,
            productCount: "24 produits",
            slug: "composite-adhesif"
        },
        {
            id: 2,
            name: "Instruments Dentaires",
            description: "Instruments de précision",
            icon: <Scissors className="h-8 w-8" />,
            image: dentalequipment,
            productCount: "45 produits",
            slug: "instruments"
        },
        {
            id: 3,
            name: "Équipement Médical",
            description: "Technologies avancées",
            icon: <Zap className="h-8 w-8" />,
            image: dentalequipment,
            productCount: "18 produits",
            slug: "equipement"
        },
        {
            id: 4,
            name: "Stérilisation",
            description: "Solutions d'hygiène",
            icon: <Shield className="h-8 w-8" />,
            image: dentalequipment,
            productCount: "32 produits",
            slug: "sterilisation"
        },
        {
            id: 5,
            name: "Diagnostic",
            description: "Outils de diagnostic",
            icon: <Microscope className="h-8 w-8" />,
            image: dentalequipment,
            productCount: "28 produits",
            slug: "diagnostic"
        },
        {
            id: 6,
            name: "Anesthésie",
            description: "Produits anesthésiques",
            icon: <Syringe className="h-8 w-8" />,
            image: dentalequipment,
            productCount: "15 produits",
            slug: "anesthesie"
        }
    ];

    return (
        <section className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <Badge className="mb-4 bg-blue-100 text-blue-700 hover:bg-blue-200">
                        Nos produits
                    </Badge>
                    
                    <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Catégories de produits
                    </h2>
                    
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Découvrez notre large gamme d'équipements médicaux et dentaires 
                        de haute qualité, adaptés à tous vos besoins professionnels.
                    </p>
                </motion.div>

                {/* Product Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {categories.map((category, index) => (
                        <motion.div
                            key={category.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true }}
                            className="group"
                        >
                            <Card className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 group-hover:-translate-y-2">
                                {/* Image */}
                                <div className="relative h-48 overflow-hidden">
                                    <Image
                                        src={category.image}
                                        alt={category.name}
                                        fill
                                        className="object-cover transition-transform duration-700 group-hover:scale-110"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                                    
                                    {/* Icon */}
                                    <div className="absolute top-4 left-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-blue-600">
                                        {category.icon}
                                    </div>
                                    
                                    {/* Product Count */}
                                    <div className="absolute bottom-4 right-4">
                                        <Badge className="bg-white/90 text-gray-700 backdrop-blur-sm">
                                            {category.productCount}
                                        </Badge>
                                    </div>
                                </div>

                                {/* Content */}
                                <div className="p-6">
                                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                        {category.name}
                                    </h3>
                                    <p className="text-gray-600 mb-4">
                                        {category.description}
                                    </p>
                                    
                                    <Button
                                        variant="ghost"
                                        className="w-full justify-between text-blue-600 hover:bg-blue-50 hover:text-blue-700 group/btn"
                                        asChild
                                    >
                                        <Link href={`/catalog?category=${category.slug}`}>
                                            Voir les produits
                                            <ArrowRight className="h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                                        </Link>
                                    </Button>
                                </div>
                            </Card>
                        </motion.div>
                    ))}
                </div>

                {/* CTA Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="text-center mt-16"
                >
                    <Button
                        size="lg"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                        asChild
                    >
                        <Link href="/catalog">
                            Voir tous les produits
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}

export default ProductGridSection;
