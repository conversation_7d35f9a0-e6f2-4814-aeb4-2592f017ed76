"use client";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
    ArrowRight,
    Heart,
    Scissors,
    Zap,
    Shield,
    Microscope,
    Syringe,
    Package,
    Loader2
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";
import { useState, useEffect } from "react";

// Icon mapping for categories
const iconMap: { [key: string]: React.ReactNode } = {
    'heart': <Heart className="h-8 w-8" />,
    'scissors': <Scissors className="h-8 w-8" />,
    'zap': <Zap className="h-8 w-8" />,
    'shield': <Shield className="h-8 w-8" />,
    'microscope': <Microscope className="h-8 w-8" />,
    'syringe': <Syringe className="h-8 w-8" />,
    'package': <Package className="h-8 w-8" />,
};

interface Category {
    id: string;
    name: string;
    description: string | null;
    slug: string;
    icon: string | null;
    image: string | null;
    color: string | null;
    isActive: boolean;
    _count: {
        products: number;
    };
}

function ProductGridSection() {
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/categories/public');

            if (!response.ok) {
                throw new Error('Failed to fetch categories');
            }

            const data = await response.json();
            setCategories(data.categories || []);
        } catch (err) {
            console.error('Error fetching categories:', err);
            setError('Failed to load categories');
            // Set some mock data as fallback
            setCategories([
                {
                    id: '1',
                    name: 'Composite & Adhésif',
                    description: 'Matériaux de restauration dentaire',
                    slug: 'composite-adhesif',
                    icon: 'heart',
                    image: null,
                    color: null,
                    isActive: true,
                    _count: { products: 24 }
                },
                {
                    id: '2',
                    name: 'Instruments Dentaires',
                    description: 'Instruments de précision',
                    slug: 'instruments',
                    icon: 'scissors',
                    image: null,
                    color: null,
                    isActive: true,
                    _count: { products: 45 }
                },
                {
                    id: '3',
                    name: 'Équipement Médical',
                    description: 'Technologies avancées',
                    slug: 'equipement',
                    icon: 'zap',
                    image: null,
                    color: null,
                    isActive: true,
                    _count: { products: 18 }
                },
                {
                    id: '4',
                    name: 'Stérilisation',
                    description: 'Solutions d\'hygiène',
                    slug: 'sterilisation',
                    icon: 'shield',
                    image: null,
                    color: null,
                    isActive: true,
                    _count: { products: 32 }
                },
                {
                    id: '5',
                    name: 'Diagnostic',
                    description: 'Outils de diagnostic',
                    slug: 'diagnostic',
                    icon: 'microscope',
                    image: null,
                    color: null,
                    isActive: true,
                    _count: { products: 28 }
                },
                {
                    id: '6',
                    name: 'Anesthésie',
                    description: 'Produits anesthésiques',
                    slug: 'anesthesie',
                    icon: 'syringe',
                    image: null,
                    color: null,
                    isActive: true,
                    _count: { products: 15 }
                }
            ]);
        } finally {
            setLoading(false);
        }
    };

    const getIcon = (iconName: string | null) => {
        if (!iconName) return <Package className="h-8 w-8" />;
        return iconMap[iconName.toLowerCase()] || <Package className="h-8 w-8" />;
    };

    const getImage = (imageUrl: string | null) => {
        return imageUrl || dentalequipment;
    };

    // Loading state
    if (loading) {
        return (
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <Badge className="mb-4 bg-blue-100 text-blue-700">
                            Nos produits
                        </Badge>
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            Catégories de produits
                        </h2>
                    </div>
                    <div className="flex items-center justify-center min-h-[400px]">
                        <div className="text-center">
                            <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
                            <p className="text-gray-600">Chargement des catégories...</p>
                        </div>
                    </div>
                </div>
            </section>
        );
    }

    // Error state
    if (error) {
        return (
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <p className="text-red-600 mb-4">Erreur: {error}</p>
                        <Button onClick={fetchCategories} variant="outline">
                            Réessayer
                        </Button>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <section className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <Badge className="mb-4 bg-blue-100 text-blue-700 hover:bg-blue-200">
                        Nos produits
                    </Badge>

                    <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Catégories de produits
                    </h2>

                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Découvrez notre large gamme d'équipements médicaux et dentaires
                        de haute qualité, adaptés à tous vos besoins professionnels.
                    </p>
                </motion.div>

                {/* Product Grid */}
                {categories.length === 0 ? (
                    <div className="text-center py-12">
                        <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600 text-lg">Aucune catégorie disponible pour le moment.</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {categories.map((category, index) => (
                            <motion.div
                                key={category.id}
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                viewport={{ once: true }}
                                className="group"
                            >
                                <Card className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 group-hover:-translate-y-2">
                                    {/* Image */}
                                    <div className="relative h-48 overflow-hidden">
                                        <Image
                                            src={getImage(category.image)}
                                            alt={category.name}
                                            fill
                                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                                        />
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                                        {/* Icon */}
                                        <div className="absolute top-4 left-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-blue-600">
                                            {getIcon(category.icon)}
                                        </div>

                                        {/* Product Count */}
                                        <div className="absolute bottom-4 right-4">
                                            <Badge className="bg-white/90 text-gray-700 backdrop-blur-sm">
                                                {category._count.products} produit{category._count.products !== 1 ? 's' : ''}
                                            </Badge>
                                        </div>
                                    </div>

                                    {/* Content */}
                                    <div className="p-6">
                                        <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                            {category.name}
                                        </h3>
                                        <p className="text-gray-600 mb-4">
                                            {category.description || "Découvrez nos produits de qualité"}
                                        </p>

                                        <Button
                                            variant="ghost"
                                            className="w-full justify-between text-blue-600 hover:bg-blue-50 hover:text-blue-700 group/btn"
                                            asChild
                                        >
                                            <Link href={`/catalog?category=${category.slug}`}>
                                                Voir les produits
                                                <ArrowRight className="h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                                            </Link>
                                        </Button>
                                    </div>
                                </Card>
                            </motion.div>
                        ))}
                    </div>
                )}

                {/* CTA Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="text-center mt-16"
                >
                    <Button
                        size="lg"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                        asChild
                    >
                        <Link href="/catalog">
                            Voir tous les produits
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}

export default ProductGridSection;
