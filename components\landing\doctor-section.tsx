"use client";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
    Phone,
    Mail,
    MapPin,
    Clock,
    ArrowRight,
    CheckCircle,
    Award,
    Users
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";

function DoctorSection() {
    const services = [
        "Consultation et conseil personnalisé",
        "Formation sur l'utilisation des équipements",
        "Service après-vente réactif",
        "Maintenance et réparation",
        "Livraison rapide en Tunisie"
    ];

    const stats = [
        { number: "500+", label: "Professionnels formés" },
        { number: "15+", label: "Années d'expérience" },
        { number: "24/7", label: "Support technique" }
    ];

    return (
        <section className="py-20 bg-gradient-to-br from-green-50 to-green-100">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    {/* Image Section */}
                    <motion.div
                        initial={{ opacity: 0, x: -30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                        className="relative"
                    >
                        <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                            <Image
                                src={dentalequipment}
                                alt="Professionnel de santé avec équipements"
                                width={600}
                                height={500}
                                className="w-full h-[500px] object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-green-900/30 to-transparent"></div>
                            
                            {/* Floating Stats */}
                            <div className="absolute bottom-6 left-6 right-6">
                                <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4">
                                    <div className="grid grid-cols-3 gap-4 text-center">
                                        {stats.map((stat, index) => (
                                            <div key={index}>
                                                <div className="text-lg font-bold text-gray-900">{stat.number}</div>
                                                <div className="text-xs text-gray-600">{stat.label}</div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {/* Floating Badge */}
                        <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            viewport={{ once: true }}
                            className="absolute -top-6 -right-6 bg-green-600 text-white rounded-full p-4 shadow-xl"
                        >
                            <Award className="h-8 w-8" />
                        </motion.div>
                    </motion.div>

                    {/* Content Section */}
                    <motion.div
                        initial={{ opacity: 0, x: 30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        viewport={{ once: true }}
                        className="space-y-8"
                    >
                        <div>
                            <Badge className="mb-4 bg-green-100 text-green-700 hover:bg-green-200">
                                <Users className="h-4 w-4 mr-2" />
                                Accompagnement professionnel
                            </Badge>
                            
                            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                                Un accompagnement d'expert à chaque étape
                            </h2>
                            
                            <p className="text-lg text-gray-700 leading-relaxed mb-6">
                                Notre équipe d'experts vous accompagne dans le choix, l'installation et 
                                l'utilisation de vos équipements médicaux. Bénéficiez d'un service 
                                personnalisé et d'une expertise reconnue dans le domaine médical.
                            </p>
                        </div>

                        {/* Services List */}
                        <div className="space-y-4">
                            {services.map((service, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, x: 20 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: index * 0.1 }}
                                    viewport={{ once: true }}
                                    className="flex items-center gap-3"
                                >
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-gray-700">{service}</span>
                                </motion.div>
                            ))}
                        </div>

                        {/* Contact Info */}
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-green-200">
                            <h3 className="text-xl font-bold text-gray-900 mb-4">
                                Contactez nos experts
                            </h3>
                            
                            <div className="space-y-3">
                                <div className="flex items-center gap-3 text-gray-600">
                                    <Phone className="h-5 w-5 text-green-600" />
                                    <span>+216 XX XXX XXX</span>
                                </div>
                                <div className="flex items-center gap-3 text-gray-600">
                                    <Mail className="h-5 w-5 text-green-600" />
                                    <span><EMAIL></span>
                                </div>
                                <div className="flex items-center gap-3 text-gray-600">
                                    <MapPin className="h-5 w-5 text-green-600" />
                                    <span>Tunis, Tunisie</span>
                                </div>
                                <div className="flex items-center gap-3 text-gray-600">
                                    <Clock className="h-5 w-5 text-green-600" />
                                    <span>Lun-Ven: 8h-18h, Sam: 8h-13h</span>
                                </div>
                            </div>
                        </div>

                        {/* CTA Buttons */}
                        <div className="flex flex-col sm:flex-row gap-4">
                            <Button
                                size="lg"
                                className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                                asChild
                            >
                                <Link href="/contact">
                                    Demander un devis
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                            </Button>
                            
                            <Button
                                size="lg"
                                variant="outline"
                                className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-3 rounded-lg transition-all duration-300"
                                asChild
                            >
                                <Link href="/services">
                                    Nos services
                                </Link>
                            </Button>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}

export default DoctorSection;
