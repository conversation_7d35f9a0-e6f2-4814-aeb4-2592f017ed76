"use client";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
    ArrowRight,
    Star,
    Zap,
    Shield,
    Award,
    Truck,
    HeadphonesIcon,
    CheckCircle
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";

function EquipmentShowcase() {
    const equipmentCategories = [
        {
            id: 1,
            title: "Équipements de Diagnostic",
            subtitle: "Technologies de pointe pour un diagnostic précis",
            products: [
                {
                    name: "Radiographie Numérique",
                    price: "2,499€",
                    features: ["Haute résolution", "Faible radiation", "Interface intuitive"],
                    image: dentalequipment
                },
                {
                    name: "Scanner Intra-oral",
                    price: "1,899€", 
                    features: ["3D haute précision", "Temps de scan rapide", "Confort patient"],
                    image: dentalequipment
                },
                {
                    name: "Caméra Intra-orale",
                    price: "899€",
                    features: ["HD 1080p", "LED intégrées", "Ergonomique"],
                    image: dentalequipment
                }
            ]
        },
        {
            id: 2,
            title: "Instruments Chirurgicaux",
            subtitle: "Précision et fiabilité pour vos interventions",
            products: [
                {
                    name: "Kit Implantologie",
                    price: "1,299€",
                    features: ["Acier chirurgical", "Stérilisable", "Garantie 5 ans"],
                    image: dentalequipment
                },
                {
                    name: "Fraises Diamantées",
                    price: "89€",
                    features: ["Longue durée", "Coupe précise", "Compatible tous systèmes"],
                    image: dentalequipment
                },
                {
                    name: "Instruments Endodontie",
                    price: "299€",
                    features: ["Flexibilité optimale", "Résistance élevée", "Marquage laser"],
                    image: dentalequipment
                }
            ]
        }
    ];

    const features = [
        {
            icon: <Shield className="h-6 w-6" />,
            title: "Garantie Étendue",
            description: "Jusqu'à 5 ans de garantie sur nos équipements"
        },
        {
            icon: <Truck className="h-6 w-6" />,
            title: "Livraison Rapide",
            description: "Livraison en 24-48h partout en Tunisie"
        },
        {
            icon: <HeadphonesIcon className="h-6 w-6" />,
            title: "Support 24/7",
            description: "Assistance technique disponible en permanence"
        },
        {
            icon: <Award className="h-6 w-6" />,
            title: "Certifié CE",
            description: "Tous nos produits sont certifiés aux normes européennes"
        }
    ];

    return (
        <section className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <Badge className="mb-4 bg-blue-100 text-blue-700 hover:bg-blue-200">
                        <Zap className="h-4 w-4 mr-2" />
                        Équipements professionnels
                    </Badge>
                    
                    <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Technologies de pointe pour votre pratique
                    </h2>
                    
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Découvrez notre sélection d'équipements médicaux et dentaires 
                        de dernière génération, conçus pour optimiser vos performances.
                    </p>
                </motion.div>

                {/* Equipment Categories */}
                <div className="space-y-20">
                    {equipmentCategories.map((category, categoryIndex) => (
                        <motion.div
                            key={category.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: categoryIndex * 0.2 }}
                            viewport={{ once: true }}
                        >
                            {/* Category Header */}
                            <div className="text-center mb-12">
                                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                                    {category.title}
                                </h3>
                                <p className="text-lg text-gray-600">
                                    {category.subtitle}
                                </p>
                            </div>

                            {/* Products Grid */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {category.products.map((product, productIndex) => (
                                    <motion.div
                                        key={productIndex}
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5, delay: productIndex * 0.1 }}
                                        viewport={{ once: true }}
                                        className="group"
                                    >
                                        <Card className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 group-hover:-translate-y-2">
                                            {/* Image */}
                                            <div className="relative h-48 overflow-hidden">
                                                <Image
                                                    src={product.image}
                                                    alt={product.name}
                                                    fill
                                                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                                                />
                                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                                                
                                                {/* Price Badge */}
                                                <div className="absolute top-4 right-4">
                                                    <Badge className="bg-blue-600 text-white">
                                                        {product.price}
                                                    </Badge>
                                                </div>
                                            </div>

                                            {/* Content */}
                                            <div className="p-6">
                                                <h4 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                                                    {product.name}
                                                </h4>
                                                
                                                {/* Features */}
                                                <div className="space-y-2 mb-4">
                                                    {product.features.map((feature, idx) => (
                                                        <div key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                                                            <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                                                            {feature}
                                                        </div>
                                                    ))}
                                                </div>

                                                {/* Rating */}
                                                <div className="flex items-center gap-2 mb-4">
                                                    <div className="flex items-center">
                                                        {[...Array(5)].map((_, i) => (
                                                            <Star
                                                                key={i}
                                                                className="h-4 w-4 text-yellow-400 fill-current"
                                                            />
                                                        ))}
                                                    </div>
                                                    <span className="text-sm text-gray-600">4.9 (32 avis)</span>
                                                </div>

                                                {/* CTA Button */}
                                                <Button
                                                    className="w-full bg-blue-600 hover:bg-blue-700 text-white group/btn"
                                                    asChild
                                                >
                                                    <Link href={`/products/${product.name.toLowerCase().replace(/\s+/g, '-')}`}>
                                                        Voir les détails
                                                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                                                    </Link>
                                                </Button>
                                            </div>
                                        </Card>
                                    </motion.div>
                                ))}
                            </div>
                        </motion.div>
                    ))}
                </div>

                {/* Features Section */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="mt-20"
                >
                    <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
                        <div className="text-center mb-12">
                            <h3 className="text-3xl font-bold text-gray-900 mb-4">
                                Pourquoi choisir nos équipements ?
                            </h3>
                            <p className="text-lg text-gray-600">
                                Des avantages qui font la différence pour votre pratique
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5, delay: index * 0.1 }}
                                    viewport={{ once: true }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 text-blue-600">
                                        {feature.icon}
                                    </div>
                                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                                        {feature.title}
                                    </h4>
                                    <p className="text-gray-600 text-sm">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}

export default EquipmentShowcase;
