"use client";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ton } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
    Star,
    ShoppingCart,
    Eye,
    Heart,
    ArrowRight,
    CheckCircle
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";

function FeaturedProductsSection() {
    const featuredProducts = [
        {
            id: 1,
            name: "Composite Universel Premium",
            description: "Composite dentaire haute résistance pour restaurations esthétiques",
            price: "89.99",
            originalPrice: "109.99",
            rating: 4.8,
            reviews: 124,
            image: dentalequipment,
            badge: "Bestseller",
            features: ["Haute résistance", "Esthétique naturelle", "Facile à polir"]
        },
        {
            id: 2,
            name: "Kit Instruments Chirurgicaux",
            description: "Set complet d'instruments chirurgicaux en acier inoxydable",
            price: "299.99",
            originalPrice: "349.99",
            rating: 4.9,
            reviews: 89,
            image: dentalequipment,
            badge: "Nouveau",
            features: ["Acier inoxydable", "Stérilisable", "Garantie 2 ans"]
        },
        {
            id: 3,
            name: "Lampe de Polymérisation LED",
            description: "Lampe LED haute puissance pour polymérisation rapide",
            price: "199.99",
            originalPrice: "249.99",
            rating: 4.7,
            reviews: 156,
            image: dentalequipment,
            badge: "Promo",
            features: ["LED haute puissance", "Batterie longue durée", "Léger et ergonomique"]
        }
    ];

    return (
        <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <Badge className="mb-4 bg-green-100 text-green-700 hover:bg-green-200">
                        Produits vedettes
                    </Badge>
                    
                    <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Nos meilleures ventes
                    </h2>
                    
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Découvrez les produits les plus appréciés par nos clients professionnels
                    </p>
                </motion.div>

                {/* Products Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {featuredProducts.map((product, index) => (
                        <motion.div
                            key={product.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true }}
                            className="group"
                        >
                            <Card className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 group-hover:-translate-y-2">
                                {/* Image */}
                                <div className="relative h-64 overflow-hidden">
                                    <Image
                                        src={product.image}
                                        alt={product.name}
                                        fill
                                        className="object-cover transition-transform duration-700 group-hover:scale-110"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                                    
                                    {/* Badge */}
                                    <div className="absolute top-4 left-4">
                                        <Badge className={`
                                            ${product.badge === 'Bestseller' ? 'bg-orange-500 text-white' : ''}
                                            ${product.badge === 'Nouveau' ? 'bg-green-500 text-white' : ''}
                                            ${product.badge === 'Promo' ? 'bg-red-500 text-white' : ''}
                                        `}>
                                            {product.badge}
                                        </Badge>
                                    </div>
                                    
                                    {/* Action Buttons */}
                                    <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <Button size="icon" variant="secondary" className="w-10 h-10 rounded-full bg-white/90 hover:bg-white">
                                            <Heart className="h-4 w-4" />
                                        </Button>
                                        <Button size="icon" variant="secondary" className="w-10 h-10 rounded-full bg-white/90 hover:bg-white">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                {/* Content */}
                                <div className="p-6">
                                    {/* Rating */}
                                    <div className="flex items-center gap-2 mb-3">
                                        <div className="flex items-center">
                                            {[...Array(5)].map((_, i) => (
                                                <Star
                                                    key={i}
                                                    className={`h-4 w-4 ${
                                                        i < Math.floor(product.rating)
                                                            ? 'text-yellow-400 fill-current'
                                                            : 'text-gray-300'
                                                    }`}
                                                />
                                            ))}
                                        </div>
                                        <span className="text-sm text-gray-600">
                                            {product.rating} ({product.reviews} avis)
                                        </span>
                                    </div>

                                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                        {product.name}
                                    </h3>
                                    
                                    <p className="text-gray-600 mb-4 text-sm">
                                        {product.description}
                                    </p>

                                    {/* Features */}
                                    <div className="space-y-1 mb-4">
                                        {product.features.map((feature, idx) => (
                                            <div key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                                                <CheckCircle className="h-3 w-3 text-green-500" />
                                                {feature}
                                            </div>
                                        ))}
                                    </div>

                                    {/* Price */}
                                    <div className="flex items-center gap-2 mb-4">
                                        <span className="text-2xl font-bold text-blue-600">
                                            {product.price}€
                                        </span>
                                        <span className="text-lg text-gray-400 line-through">
                                            {product.originalPrice}€
                                        </span>
                                    </div>

                                    {/* Add to Cart Button */}
                                    <Button
                                        className="w-full bg-blue-600 hover:bg-blue-700 text-white group/btn"
                                        asChild
                                    >
                                        <Link href={`/products/${product.id}`}>
                                            <ShoppingCart className="h-4 w-4 mr-2" />
                                            Ajouter au panier
                                        </Link>
                                    </Button>
                                </div>
                            </Card>
                        </motion.div>
                    ))}
                </div>

                {/* CTA Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="text-center mt-16"
                >
                    <Button
                        size="lg"
                        variant="outline"
                        className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-lg transition-all duration-300"
                        asChild
                    >
                        <Link href="/products">
                            Voir tous les produits
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}

export default FeaturedProductsSection;
