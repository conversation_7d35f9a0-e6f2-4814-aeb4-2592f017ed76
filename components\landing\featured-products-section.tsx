"use client";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
    Star,
    ShoppingCart,
    Eye,
    Heart,
    ArrowRight,
    CheckCircle,
    Loader2,
    Package
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import dentalequipment from "@/public/images/dental-equipment.jpg";
import { useState, useEffect } from "react";

interface Product {
    id: string;
    name: string;
    description: string | null;
    price: string;
    comparePrice: string | null;
    thumbnail: string | null;
    images: string[];
    isFeatured: boolean;
    status: string;
    category?: {
        id: string;
        name: string;
        slug: string;
    };
}

function FeaturedProductsSection() {
    const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchFeaturedProducts();
    }, []);

    const fetchFeaturedProducts = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/products/public?featured=true&limit=6');

            if (!response.ok) {
                throw new Error('Failed to fetch featured products');
            }

            const data = await response.json();
            setFeaturedProducts(data.products || []);
        } catch (err) {
            console.error('Error fetching featured products:', err);
            setError('Failed to load featured products');
            // Set some mock data as fallback
            setFeaturedProducts([
                {
                    id: '1',
                    name: 'Composite Dentaire Premium',
                    description: 'Composite haute qualité pour restaurations esthétiques',
                    price: '89.99',
                    comparePrice: '109.99',
                    thumbnail: null,
                    images: [],
                    isFeatured: true,
                    status: 'PUBLISHED'
                },
                {
                    id: '2',
                    name: 'Kit Instruments Chirurgicaux',
                    description: 'Set complet d\'instruments en acier inoxydable',
                    price: '299.99',
                    comparePrice: '349.99',
                    thumbnail: null,
                    images: [],
                    isFeatured: true,
                    status: 'PUBLISHED'
                },
                {
                    id: '3',
                    name: 'Lampe LED Polymérisation',
                    description: 'Lampe LED haute puissance pour polymérisation rapide',
                    price: '199.99',
                    comparePrice: '249.99',
                    thumbnail: null,
                    images: [],
                    isFeatured: true,
                    status: 'PUBLISHED'
                }
            ]);
        } finally {
            setLoading(false);
        }
    };

    const getProductImage = (product: Product) => {
        return product.thumbnail || product.images[0] || dentalequipment;
    };

    const formatPrice = (price: string) => {
        return parseFloat(price).toFixed(2);
    };

    const getRandomRating = () => {
        return (4.5 + Math.random() * 0.5).toFixed(1);
    };

    const getRandomReviews = () => {
        return Math.floor(Math.random() * 200) + 50;
    };

    const getBadge = (product: Product, index: number) => {
        if (product.comparePrice && parseFloat(product.comparePrice) > parseFloat(product.price)) {
            return "Promo";
        }
        if (index === 0) return "Bestseller";
        if (index === 1) return "Nouveau";
        return null;
    };

    const getFeatures = (product: Product) => {
        // Generate some default features based on product name/description
        const features = [];
        const name = product.name.toLowerCase();
        const desc = product.description?.toLowerCase() || '';

        if (name.includes('composite') || desc.includes('composite')) {
            features.push("Haute résistance", "Esthétique naturelle", "Facile à polir");
        } else if (name.includes('instrument') || desc.includes('instrument')) {
            features.push("Acier inoxydable", "Stérilisable", "Garantie 2 ans");
        } else if (name.includes('led') || name.includes('lampe')) {
            features.push("LED haute puissance", "Batterie longue durée", "Léger et ergonomique");
        } else {
            features.push("Qualité premium", "Certifié CE", "Livraison rapide");
        }

        return features.slice(0, 3);
    };

    // Loading state
    if (loading) {
        return (
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <Badge className="mb-4 bg-purple-100 text-purple-700">
                            Produits vedettes
                        </Badge>
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            Nos meilleures ventes
                        </h2>
                    </div>
                    <div className="flex items-center justify-center min-h-[400px]">
                        <div className="text-center">
                            <Loader2 className="h-12 w-12 animate-spin text-green-600 mx-auto mb-4" />
                            <p className="text-gray-600">Chargement des produits vedettes...</p>
                        </div>
                    </div>
                </div>
            </section>
        );
    }

    // Error state
    if (error) {
        return (
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <p className="text-red-600 mb-4">Erreur: {error}</p>
                        <Button onClick={fetchFeaturedProducts} variant="outline">
                            Réessayer
                        </Button>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <Badge className="mb-4 bg-purple-100 text-purple-700 hover:bg-purple-200">
                        Produits vedettes
                    </Badge>

                    <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Nos meilleures ventes
                    </h2>

                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Découvrez les produits les plus appréciés par nos clients professionnels
                    </p>
                </motion.div>

                {/* Products Grid */}
                {featuredProducts.length === 0 ? (
                    <div className="text-center py-12">
                        <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600 text-lg">Aucun produit vedette disponible pour le moment.</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {featuredProducts.slice(0, 3).map((product, index) => {
                            const badge = getBadge(product, index);
                            const rating = getRandomRating();
                            const reviews = getRandomReviews();
                            const features = getFeatures(product);

                            return (
                                <motion.div
                                    key={product.id}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    viewport={{ once: true }}
                                    className="group"
                                >
                                    <Card className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 group-hover:-translate-y-2">
                                        {/* Image */}
                                        <div className="relative h-64 overflow-hidden">
                                            <Image
                                                src={getProductImage(product)}
                                                alt={product.name}
                                                fill
                                                className="object-cover transition-transform duration-700 group-hover:scale-110"
                                            />
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

                                            {/* Badge */}
                                            {badge && (
                                                <div className="absolute top-4 left-4">
                                                    <Badge className={`
                                                        ${badge === 'Bestseller' ? 'bg-orange-500 text-white' : ''}
                                                        ${badge === 'Nouveau' ? 'bg-purple-500 text-white' : ''}
                                                        ${badge === 'Promo' ? 'bg-red-500 text-white' : ''}
                                                    `}>
                                                        {badge}
                                                    </Badge>
                                                </div>
                                            )}

                                            {/* Action Buttons */}
                                            <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                <Button size="icon" variant="secondary" className="w-10 h-10 rounded-full bg-white/90 hover:bg-white">
                                                    <Heart className="h-4 w-4" />
                                                </Button>
                                                <Button size="icon" variant="secondary" className="w-10 h-10 rounded-full bg-white/90 hover:bg-white">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>

                                        {/* Content */}
                                        <div className="p-6">
                                            {/* Rating */}
                                            <div className="flex items-center gap-2 mb-3">
                                                <div className="flex items-center">
                                                    {[...Array(5)].map((_, i) => (
                                                        <Star
                                                            key={i}
                                                            className={`h-4 w-4 ${
                                                                i < Math.floor(parseFloat(rating))
                                                                    ? 'text-yellow-400 fill-current'
                                                                    : 'text-gray-300'
                                                            }`}
                                                        />
                                                    ))}
                                                </div>
                                                <span className="text-sm text-gray-600">
                                                    {rating} ({reviews} avis)
                                                </span>
                                            </div>

                                            <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                                {product.name}
                                            </h3>

                                            <p className="text-gray-600 mb-4 text-sm">
                                                {product.description || "Produit de qualité professionnelle"}
                                            </p>

                                            {/* Features */}
                                            <div className="space-y-1 mb-4">
                                                {features.map((feature, idx) => (
                                                    <div key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                                                        <CheckCircle className="h-3 w-3 text-purple-500" />
                                                        {feature}
                                                    </div>
                                                ))}
                                            </div>

                                            {/* Price */}
                                            <div className="flex items-center gap-2 mb-4">
                                                <span className="text-2xl font-bold text-blue-600">
                                                    {formatPrice(product.price)}€
                                                </span>
                                                {product.comparePrice && (
                                                    <span className="text-lg text-gray-400 line-through">
                                                        {formatPrice(product.comparePrice)}€
                                                    </span>
                                                )}
                                            </div>

                                            {/* Add to Cart Button */}
                                            <Button
                                                className="w-full bg-blue-600 hover:bg-blue-700 text-white group/btn"
                                                asChild
                                            >
                                                <Link href={`/products/${product.id}`}>
                                                    <ShoppingCart className="h-4 w-4 mr-2" />
                                                    Ajouter au panier
                                                </Link>
                                            </Button>
                                        </div>
                                    </Card>
                                </motion.div>
                            );
                        })}
                    </div>
                )}

                {/* CTA Section */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="text-center mt-16"
                >
                    <Button
                        size="lg"
                        variant="outline"
                        className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-lg transition-all duration-300"
                        asChild
                    >
                        <Link href="/products">
                            Voir tous les produits
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}

export default FeaturedProductsSection;
